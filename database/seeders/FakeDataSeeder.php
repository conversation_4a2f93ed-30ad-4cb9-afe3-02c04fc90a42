<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Faker\Factory as Faker;

class FakeDataSeeder extends Seeder
{
    public function run(): void
    {
        $faker = Faker::create('pt_BR');

        // Criar usuários
        $users = [];
        for ($i = 0; $i < 10; $i++) {
            $users[] = [
                'first_name' => $faker->firstName(),
                'last_name' => $faker->lastName(),
                'email' => $faker->unique()->safeEmail(),
                'email_verified_at' => now(),
                'role' => $faker->randomElement(['user', 'customer', 'admin']),
                'status' => $faker->randomElement(['active', 'inactive']),
                'password' => Hash::make('password'),
                'phone' => $faker->unique()->cellphoneNumber(),
                'document' => $faker->unique()->cpf(),
                'agreement_terms' => true,
                'wehook_url' => $faker->optional()->url(),
                'company_name' => $faker->optional()->company(),
                'remember_token' => Str::random(10),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('users')->insert($users);

        // Criar API Keys
        $apiKeys = [];
        foreach (DB::table('users')->get() as $user) {
            $apiKeys[] = [
                'user_id' => $user->id,
                'key' => Str::random(32),
                'secret' => Str::random(64),
                'description' => $faker->sentence(),
                'status' => $faker->randomElement(['active', 'inactive']),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('api_keys')->insert($apiKeys);

        // Criar contas PIX
        $pixAccounts = [];
        foreach (DB::table('users')->get() as $user) {
            $pixAccounts[] = [
                'user_id' => $user->id,
                'bank_code' => $faker->randomElement(['001', '033', '104', '237', '341', '356', '422', '633', '748', '756']),
                'account_number' => $faker->bankAccountNumber(),
                'account_type' => $faker->randomElement(['corrente', 'poupanca']),
                'is_active' => true,
                'balance' => $faker->randomFloat(2, 0, 10000),
                'pix_key' => $faker->unique()->email(),
                'pix_key_type' => 'email',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('pix_accounts')->insert($pixAccounts);

        // Criar transações PIX
        $pixTransactions = [];
        foreach (DB::table('pix_accounts')->get() as $account) {
            for ($i = 0; $i < 5; $i++) {
                $pixTransactions[] = [
                    'user_id' => $account->user_id,
                    'pix_account_id' => $account->id,
                    'pix_transaction_id' => (string) Str::uuid(),
                    'order_id' => $faker->optional()->uuid(),
                    'transaction_id_external' => $faker->optional()->uuid(),
                    'type' => $faker->randomElement(['send', 'receive']),
                    'amount' => $faker->randomFloat(2, 1, 1000),
                    'status' => $faker->randomElement(['pending', 'completed', 'failed']),
                    'description' => $faker->sentence(),
                    'pix_key' => $faker->email(),
                    'qr_code' => $faker->optional()->imageUrl(),
                    'created_at' => $faker->dateTimeBetween('-1 month', 'now'),
                    'updated_at' => now(),
                ];
            }
        }
        DB::table('pix_transactions')->insert($pixTransactions);

        // Criar eventos de webhook
        $webhookEvents = [];
        foreach (DB::table('pix_transactions')->get() as $transaction) {
            $webhookEvents[] = [
                'user_id' => $transaction->user_id,
                'pix_transaction_id' => $transaction->id,
                'event_type' => $faker->randomElement(['transaction.created', 'transaction.completed', 'transaction.failed']),
                'status' => $faker->randomElement(['pending', 'sent', 'failed']),
                'payload' => json_encode([
                    'transaction_id' => $transaction->pix_transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'timestamp' => now()->toIso8601String(),
                ]),
                'created_at' => $transaction->created_at,
                'updated_at' => now(),
            ];
        }
        DB::table('webhook_events')->insert($webhookEvents);
    }
}
