<?php

use App\Http\Controllers\API\V1\AuthController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\V1\ApiKeyController;
use App\Http\Controllers\API\V1\PixAccountController;
use App\Http\Controllers\API\V1\PixTransactionController;
use App\Http\Controllers\API\V1\UserController;

Route::prefix('v1')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::post('/register', [AuthController::class, 'register']);
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/reset-password', [AuthController::class, 'resetPassword']);
        Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
    });

    // Rotas protegidas por API Key
    Route::middleware('api.key')->group(function () {
        // Rotas de API Keys
        Route::prefix('api-keys')->group(function () {
            Route::post('/', [ApiKeyController::class, 'store']);
            Route::get('/', [ApiKeyController::class, 'index']);
            Route::get('/{id}', [ApiKeyController::class, 'show']);
            Route::post('/{id}/deactivate', [ApiKeyController::class, 'deactivate']);
        });

        // Rotas de Contas PIX
        Route::prefix('pix-accounts')->group(function () {
            Route::post('/', [PixAccountController::class, 'store']);
            Route::get('/', [PixAccountController::class, 'index']);
            Route::get('/{id}', [PixAccountController::class, 'show']);
            Route::put('/{id}', [PixAccountController::class, 'update']);
            Route::post('/{id}/deactivate', [PixAccountController::class, 'deactivate']);
            Route::get('/{id}/balance', [PixAccountController::class, 'balance']);
        });

        // Rotas de Transações PIX
        Route::prefix('pix-transactions')->group(function () {
            Route::post('/', [PixTransactionController::class, 'store']);
            Route::post('/deposit', [PixTransactionController::class, 'deposit']);
            Route::post('/withdraw', [PixTransactionController::class, 'withdraw']);
            Route::get('/', [PixTransactionController::class, 'index']);
            Route::get('/{id}', [PixTransactionController::class, 'show']);
            Route::get('/{id}/status', [PixTransactionController::class, 'status']);
            Route::put('/{id}/status', [PixTransactionController::class, 'updateStatus']);
            Route::get('/external/{externalId}', [PixTransactionController::class, 'findByExternalId']);
        });

        // Rotas de Usuários
        Route::prefix('users')->group(function () {
            Route::get('/', [UserController::class, 'index']);
            Route::get('/paginated', [UserController::class, 'list']);
            Route::get('/{id}', [UserController::class, 'show']);
            Route::put('/{id}', [UserController::class, 'update']);
            Route::post('/{id}/deactivate', [UserController::class, 'deactivate']);
            Route::post('/{id}/activate', [UserController::class, 'activate']);
            Route::get('/email/search', [UserController::class, 'findByEmail']);
            Route::get('/document/search', [UserController::class, 'findByDocument']);
        });
    });

    Route::get('/health', function () {
        return response()->json(['status' => 'ok']);
    })->name('health');
});

