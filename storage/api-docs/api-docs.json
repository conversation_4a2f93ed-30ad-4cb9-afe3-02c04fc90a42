{"openapi": "3.0.0", "info": {"title": "API Global Core", "description": "Documentação completa da API Global Core, incluindo autenticação, gerenciamento de usuários, contas Pix e transações.", "contact": {"name": "Equipe de Desenvolvimento", "url": "https://globalcore.com", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "Servidor principal"}], "paths": {"/api/v1/api-keys": {"get": {"tags": ["API Keys"], "summary": "Listar todas as API Keys do usuário", "operationId": "a3fae17fc204758f9381e8c9c7424d24", "responses": {"200": {"description": "Lista de API Keys retornada com sucesso", "content": {"application/json": {"schema": {"properties": {"api_keys": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "key": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Erro ao listar API Keys"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "post": {"tags": ["API Keys"], "summary": "Criar nova API Key", "operationId": "1539547893a9d5f163f17be032dfd3ee", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 1000, "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "API Key criada com sucesso", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "key": {"type": "string"}, "secret": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "500": {"description": "Erro ao criar API Key"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/api-keys/{id}": {"get": {"tags": ["API Keys"], "summary": "Buscar API Key específica", "operationId": "4a84a9d44161a2086977657ccb5b562d", "parameters": [{"name": "id", "in": "path", "description": "ID da API Key", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "API Key encontrada com sucesso", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "key": {"type": "string"}, "secret": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "API Key não encontrada"}, "500": {"description": "Erro ao buscar API Key"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/api-keys/{id}/deactivate": {"post": {"tags": ["API Keys"], "summary": "Desativar API Key", "operationId": "c6842a92f869939b0c940e0dc56e80b2", "parameters": [{"name": "id", "in": "path", "description": "ID da API Key", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "API Key desativada com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "API Key não encontrada"}, "500": {"description": "Erro ao desativar API Key"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/auth/register": {"post": {"tags": ["Autenticação"], "summary": "Registrar novo usuário", "operationId": "e6820a70bd152640eefa0af0016b3d3b", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["first_name", "last_name", "email", "password", "password_confirmation"], "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "password": {"type": "string", "format": "password"}, "password_confirmation": {"type": "string", "format": "password"}, "company_name": {"type": "string"}, "phone": {"type": "string"}, "document": {"type": "string"}, "agreement_terms": {"type": "boolean"}}, "type": "object"}}}}, "responses": {"201": {"description": "Usuário registrado com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"type": "object"}, "access_token": {"type": "string"}, "token_type": {"type": "string"}}, "type": "object"}}}}, "500": {"description": "Erro ao registrar usuário"}}}}, "/api/v1/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Login do usuário", "operationId": "bab3fbc2592f5537c990acc6071592fa", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "format": "password"}}, "type": "object"}}}}, "responses": {"200": {"description": "Login realizado com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"type": "object"}, "access_token": {"type": "string"}, "token_type": {"type": "string"}}, "type": "object"}}}}, "401": {"description": "Erro ao fazer login"}}}}, "/api/v1/logout": {"post": {"tags": ["Autenticação"], "summary": "Logout do usuário autenticado", "operationId": "123b6a1d11c0b146b02d569fe51da5a8", "responses": {"200": {"description": "Logout realizado com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "500": {"description": "Erro ao fazer logout"}}, "security": [{"sanctum": []}]}}, "/api/v1/me": {"get": {"tags": ["Autenticação"], "summary": "Buscar dados do usuário autenticado", "operationId": "91951f3233c16de74ae44a484f8d53dc", "responses": {"200": {"description": "Dados do usuário retornados com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"type": "object"}}, "type": "object"}}}}, "500": {"description": "Erro ao buscar dados do usuário"}}, "security": [{"sanctum": []}]}}, "/api/v1/refresh-token": {"post": {"tags": ["Autenticação"], "summary": "Atualizar token de acesso", "operationId": "eb59281403c5888e41caa20bb044da2e", "responses": {"200": {"description": "Token atualizado com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"type": "object"}, "access_token": {"type": "string"}, "token_type": {"type": "string"}}, "type": "object"}}}}, "401": {"description": "Erro ao atualizar token"}}, "security": [{"sanctum": []}]}}, "/api/v1/change-password": {"post": {"tags": ["Autenticação"], "summary": "Alterar senha do usuário autenticado", "operationId": "1accfb162c5e0c5f751ba4051eb8b682", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["current_password", "new_password"], "properties": {"current_password": {"type": "string", "format": "password"}, "new_password": {"type": "string", "format": "password"}}, "type": "object"}}}}, "responses": {"200": {"description": "Senha alterada com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "500": {"description": "Erro ao alterar senha"}}, "security": [{"sanctum": []}]}}, "/api/v1/pix-accounts": {"get": {"tags": ["Contas PIX"], "summary": "Listar todas as contas PIX do usuário", "operationId": "1820ff6df5f6c3846a6a9e6e00cc07ac", "responses": {"200": {"description": "Lista de contas PIX retornada com sucesso", "content": {"application/json": {"schema": {"properties": {"pix_accounts": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "bank_code": {"type": "string"}, "account_number": {"type": "string"}, "account_type": {"type": "string"}, "pix_key": {"type": "string"}, "pix_key_type": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Erro ao listar contas PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-accounts/{id}": {"get": {"tags": ["Contas PIX"], "summary": "Buscar conta PIX específica", "operationId": "9f4e01cc0fb8fe3a5b7b21e489280833", "parameters": [{"name": "id", "in": "path", "description": "ID da conta PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Conta PIX encontrada com sucesso", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "bank_code": {"type": "string"}, "account_number": {"type": "string"}, "account_type": {"type": "string"}, "pix_key": {"type": "string"}, "pix_key_type": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao buscar conta PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "put": {"tags": ["Contas PIX"], "summary": "Atualizar conta PIX", "operationId": "78dcd78a2e2a6f07ed20b7b4e00f44e3", "parameters": [{"name": "id", "in": "path", "description": "ID da conta PIX", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"pix_key": {"type": "string", "maxLength": 255}, "pix_key_type": {"type": "string", "enum": ["cpf", "email", "phone", "random"]}}, "type": "object"}}}}, "responses": {"200": {"description": "Conta PIX atualizada com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao atualizar conta PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-accounts/{id}/deactivate": {"post": {"tags": ["Contas PIX"], "summary": "Desativar conta PIX", "operationId": "77d3e789aa09ce6b19feeebcbbbd8548", "parameters": [{"name": "id", "in": "path", "description": "ID da conta PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Conta PIX desativada com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao desativar conta PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-accounts/{id}/balance": {"get": {"tags": ["Contas PIX"], "summary": "Consultar saldo da conta PIX", "operationId": "3e9913478c5c60bbb2d12d5a4dd773c9", "parameters": [{"name": "id", "in": "path", "description": "ID da conta PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Saldo retornado com sucesso", "content": {"application/json": {"schema": {"properties": {"balance": {"type": "number", "format": "float"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao consultar saldo"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions": {"get": {"tags": ["Transações PIX"], "summary": "Listar transações PIX", "operationId": "82c5a072e70d85ab170a073f0cd5dba2", "parameters": [{"name": "pix_account_id", "in": "query", "description": "ID da conta PIX para filtrar transações", "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "Status das transações para filtrar", "schema": {"type": "string", "enum": ["pending", "completed", "failed"]}}, {"name": "start_date", "in": "query", "description": "Data inicial para filtrar transações", "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Data final para filtrar transações", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Lista de transações PIX retornada com sucesso", "content": {"application/json": {"schema": {"properties": {"transactions": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "pix_account_id": {"type": "integer"}, "amount": {"type": "number", "format": "float"}, "description": {"type": "string"}, "recipient_pix_key": {"type": "string"}, "recipient_pix_key_type": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Erro ao listar transações PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/{id}": {"get": {"tags": ["Transações PIX"], "summary": "Buscar transação PIX específica", "operationId": "2be2796bcb7fed9557253829585b4983", "parameters": [{"name": "id", "in": "path", "description": "ID da transação PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Transação PIX encontrada com sucesso", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "pix_account_id": {"type": "integer"}, "amount": {"type": "number", "format": "float"}, "description": {"type": "string"}, "recipient_pix_key": {"type": "string"}, "recipient_pix_key_type": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "Transação PIX não encontrada"}, "500": {"description": "Erro ao buscar transação PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/{id}/status": {"get": {"tags": ["Transações PIX"], "summary": "Buscar status da transação PIX", "operationId": "fa1a129583e51ca278cb85936a499d7e", "parameters": [{"name": "id", "in": "path", "description": "ID da transação PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Status da transação PIX retornado com sucesso", "content": {"application/json": {"schema": {"properties": {"status": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Transação PIX não encontrada"}, "500": {"description": "Erro ao buscar status da transação PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "put": {"tags": ["Transações PIX"], "summary": "Atualizar status da transação PIX", "operationId": "2ed3d866c3e660a8dcf8ee10691b3d36", "parameters": [{"name": "id", "in": "path", "description": "ID da transação PIX", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["status"], "properties": {"status": {"type": "string", "enum": ["pending", "completed", "failed"]}}, "type": "object"}}}}, "responses": {"200": {"description": "Status atualizado com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Transação PIX não encontrada"}, "500": {"description": "Erro ao atualizar status da transação PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/deposit": {"post": {"tags": ["Transações PIX"], "summary": "Realizar depósito PIX", "operationId": "96c0cc6c56e702fae7e9b50419f6c7dd", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["pix_account_id", "amount", "description"], "properties": {"pix_account_id": {"description": "ID da conta PIX de destino", "type": "integer"}, "amount": {"description": "Valor do depósito", "type": "number", "format": "float"}, "description": {"type": "string", "maxLength": 1000}, "qr_code": {"description": "QR Code do PIX (opcional)", "type": "string"}}, "type": "object"}}}}, "responses": {"201": {"description": "Depósito PIX iniciado com sucesso", "content": {"application/json": {"schema": {"properties": {"transaction": {"type": "object"}, "qr_code": {"type": "string"}, "pix_key": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao iniciar depósito PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/withdraw": {"post": {"tags": ["Transações PIX"], "summary": "Realizar saque P<PERSON>", "operationId": "e85c9c983b764d7c2b8f9370dbc19b30", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["pix_account_id", "amount", "pix_key"], "properties": {"pix_account_id": {"description": "ID da conta PIX de origem", "type": "integer"}, "amount": {"description": "Valor do saque", "type": "number", "format": "float"}, "description": {"type": "string", "maxLength": 1000}, "pix_key": {"description": "Chave PIX do destinatário", "type": "string"}}, "type": "object"}}}}, "responses": {"201": {"description": "Saque PIX iniciado com sucesso", "content": {"application/json": {"schema": {"properties": {"transaction": {"type": "object"}, "status": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "400": {"description": "<PERSON><PERSON> insuficiente"}, "500": {"description": "Erro ao iniciar saque P<PERSON>"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/users": {"get": {"tags": ["Usuários"], "summary": "Listar usuários", "operationId": "fbe4abc6115f6a931262eefde634d1f9", "parameters": [{"name": "is_active", "in": "query", "description": "Filtrar por ativo", "schema": {"type": "boolean"}}, {"name": "role", "in": "query", "description": "Filtrar por papel", "schema": {"type": "string", "enum": ["user", "admin"]}}], "responses": {"200": {"description": "Lista de usuários retornada com sucesso", "content": {"application/json": {"schema": {"properties": {"users": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Erro ao listar usuários"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/users/{id}": {"get": {"tags": ["Usuários"], "summary": "Obter dados de um usuário", "operationId": "05dabd18926420b0d890e4fc40b53825", "parameters": [{"name": "id", "in": "path", "description": "ID do usuário", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dados do usuário retornados com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "500": {"description": "Erro ao buscar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "put": {"tags": ["Usuários"], "summary": "Atualizar dados de um usuário", "operationId": "f1b6ca0c6427f48f79ba9e3abc6c4a42", "parameters": [{"name": "id", "in": "path", "description": "ID do usuário", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"first_name": {"type": "string", "maxLength": 255}, "last_name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}, "phone": {"type": "string", "maxLength": 20}, "document": {"type": "string", "maxLength": 20}}, "type": "object"}}}}, "responses": {"200": {"description": "Dados do usuário atualizados com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "422": {"description": "<PERSON><PERSON>"}, "500": {"description": "Erro ao atualizar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "post": {"tags": ["Usuários"], "summary": "Ativar um usuário", "operationId": "b4a4c8917bac6158c6a8df77d7632ad5", "parameters": [{"name": "id", "in": "path", "description": "ID do usuário", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Usuário ativado com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "500": {"description": "Erro ao ativar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}, "delete": {"tags": ["Usuários"], "summary": "Desativar um usuário", "operationId": "f6dc518aeeb1def4abae7577aa79a336", "parameters": [{"name": "id", "in": "path", "description": "ID do usuário", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Usuário desativado com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "500": {"description": "Erro ao desativar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/users/email": {"get": {"tags": ["Usuários"], "summary": "Buscar usuário por email", "operationId": "31f5f3f49dc152be9bf34bee1b141629", "parameters": [{"name": "email", "in": "query", "description": "Email do usuário", "required": true, "schema": {"type": "string", "format": "email"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> encontrado", "content": {"application/json": {"schema": {"properties": {"user": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "500": {"description": "Erro ao buscar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/users/document": {"get": {"tags": ["Usuários"], "summary": "Buscar usuário por documento", "operationId": "cd1c5cad15a05cb7ff0ba0e5ba2cf974", "parameters": [{"name": "document", "in": "query", "description": "Documento do usuário", "required": true, "schema": {"type": "string", "maxLength": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> encontrado", "content": {"application/json": {"schema": {"properties": {"user": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Usuário não encontrado"}, "500": {"description": "Erro ao buscar usuário"}}, "security": [{"apiKey": [], "apiSecret": []}]}}}, "components": {"securitySchemes": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key para autenticação", "name": "X-API-KEY", "in": "header"}, "apiSecret": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Secret para autenticação", "name": "X-API-SECRET", "in": "header"}, "sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Insira o token no formato (Bearer <token>)", "name": "Authorization", "in": "header"}}}, "tags": [{"name": "API Keys", "description": "API Keys"}, {"name": "Autenticação", "description": "Autenticação"}, {"name": "Contas PIX", "description": "Contas PIX"}, {"name": "Transações PIX", "description": "Transações PIX"}, {"name": "Usuários", "description": "Usuários"}]}