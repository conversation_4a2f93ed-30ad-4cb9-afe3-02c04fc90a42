<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\AuthService;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class AuthServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AuthService $authService;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = new User();
        $this->authService = new AuthService($this->user);
    }

    public function test_register_creates_user_and_returns_token(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $result = $this->authService->register($userData);

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_login_returns_token_for_valid_credentials(): void
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $result = $this->authService->login($credentials);

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertEquals('Bearer', $result['token_type']);
        $this->assertEquals($user->id, $result['user']->id);
    }

    public function test_login_throws_exception_for_invalid_credentials(): void
    {
        $this->expectException(ValidationException::class);

        // Create a user
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'wrong_password',
        ];

        $this->authService->login($credentials);
    }

    public function test_change_password_updates_user_password(): void
    {
        // Create a user and authenticate
        $user = User::factory()->create([
            'password' => Hash::make('old_password'),
        ]);
        $this->actingAs($user);

        $data = [
            'current_password' => 'old_password',
            'new_password' => 'new_password123',
        ];

        $request = request();
        $result = $this->authService->changePassword($request, $data);

        $this->assertTrue($result);
        $user->refresh();
        $this->assertTrue(Hash::check('new_password123', $user->password));
    }

    public function test_change_password_throws_exception_for_incorrect_current_password(): void
    {
        $this->expectException(AuthenticationException::class);

        // Create a user and authenticate
        $user = User::factory()->create([
            'password' => Hash::make('old_password'),
        ]);
        $this->actingAs($user);

        $data = [
            'current_password' => 'wrong_password',
            'new_password' => 'new_password123',
        ];

        $request = request();
        $this->authService->changePassword($request, $data);
    }
}
