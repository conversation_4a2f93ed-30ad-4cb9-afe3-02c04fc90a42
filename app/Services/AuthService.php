<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\Collection;

class AuthService
{

    public function __construct(
        protected User $user,
        protected PixAccountService $pixAccountService,
        protected ApiKeyService $apiKeyService
    ) {}


    /**
     * @param array{
     *     first_name: string,
     *     last_name: string,
     *     email: string,
     *     password: string,
     *     phone: string,
     *     document: string,
     *     agreement_terms: bool
     * } $data
     * @return array{user: User, access_token: string, token_type: string}
     */
    public function register(array $data): array
    {
        DB::beginTransaction();
        try {
            $user = $this->user->create([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'],
                'document' => $data['document'],
                'agreement_terms' => $data['agreement_terms'],
                'role' => 'user',
            ]);

            // Criação da api_key usando o ApiKeyService
            $apiKey = $this->apiKeyService->create($user);

            // Criação da conta Pix (usando 'cpf' como padrão para pix_key_type)
            $pixAccount = $this->pixAccountService->createWithType($user, 'cpf');
            if (!$pixAccount) {
                throw new \Exception('Erro ao criar conta pix.');
            }

            DB::commit();
            $token = $user->createToken('access_token')->plainTextToken;

            return [
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
                'api_key' => $apiKey->key,
                'pix_account' => $pixAccount,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param array{email: string, password: string} $credentials
     * @return array{user: User, access_token: string, token_type: string}
     */
    public function login(array $credentials): array
    {
        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['Credenciais inválidas.'],
            ]);
        }

        $user = $this->user->where('email', $credentials['email'])->first();
        $user->tokens()->delete();

        $token = $user->createToken('access_token')->plainTextToken;

        return [
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ];
    }

    public function logout(Request $request): bool
    {
        return $request->user()->currentAccessToken()->delete();
    }

    public function logoutFromAllDevices(Request $request): bool
    {
        return $request->user()->tokens()->delete();
    }

    public function me(Request $request): User
    {
        return $request->user()->load(['apiKeys', 'pixAccounts']);
    }

    /**
     * @return array{user: User, access_token: string, token_type: string}
     */
    public function refreshToken(Request $request): array
    {
        $user = $request->user();
        $request->user()->currentAccessToken()->delete();
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ];
    }

    /**
     * @param array{current_password: string, new_password: string} $data
     */
    public function changePassword(Request $request, array $data): bool
    {
        $user = $request->user();

        if (!Hash::check($data['current_password'], $user->password)) {
            throw new AuthenticationException('A senha atual está incorreta.');
        }

        $user->password = Hash::make($data['new_password']);
        return $user->save();
    }

    /**
     * @return Collection<int, \Laravel\Sanctum\PersonalAccessToken>
     */
    public function getActiveTokens(Request $request): Collection
    {
        return $request->user()->tokens;
    }

    public function revokeToken(Request $request, int|string $tokenId): bool
    {
        return $request->user()->tokens()->where('id', $tokenId)->delete();
    }
}
