<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\PixAccountService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

class PixAccountController extends Controller
{
    public function __construct(
        private PixAccountService $pixAccountService
    ) {}


    /**
     * @OA\Get(
     *     path="/api/v1/pix-accounts",
     *     summary="Listar todas as contas PIX do usuário",
     *     tags={"Contas PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Lista de contas PIX retornada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="pix_accounts",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="bank_code", type="string"),
     *                     @OA\Property(property="account_number", type="string"),
     *                     @OA\Property(property="account_type", type="string"),
     *                     @OA\Property(property="pix_key", type="string"),
     *                     @OA\Property(property="pix_key_type", type="string"),
     *                     @OA\Property(property="status", type="string"),
     *                     @OA\Property(property="created_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao listar contas PIX"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $accounts = $this->pixAccountService->list($request['auth_user']);
            return response()->json(['pix_accounts' => $accounts]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar contas PIX: ' . $e->getMessage()], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'bank_code' => 'required|string|max:10',
            'account_number' => 'required|string|max:20',
            'account_type' => 'required|string|in:checking,savings',
            'pix_key' => 'required|string|max:255',
            'pix_key_type' => 'required|string|in:cpf,email,phone,random',
        ]);

        try {
            $account = $this->pixAccountService->create($request->user(), $validated);
            return response()->json($account, 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao criar conta PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-accounts/{id}",
     *     summary="Buscar conta PIX específica",
     *     tags={"Contas PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da conta PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Conta PIX encontrada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="bank_code", type="string"),
     *             @OA\Property(property="account_number", type="string"),
     *             @OA\Property(property="account_type", type="string"),
     *             @OA\Property(property="pix_key", type="string"),
     *             @OA\Property(property="pix_key_type", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="created_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar conta PIX"
     *     )
     * )
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $account = $this->pixAccountService->findByPixAccountId($request['auth_user'], $id);

            if (!$account) {
                return response()->json(['message' => 'Conta PIX não encontrada'], 404);
            }

            return response()->json($account);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar conta PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/pix-accounts/{id}",
     *     summary="Atualizar conta PIX",
     *     tags={"Contas PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da conta PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="pix_key", type="string", maxLength=255),
     *             @OA\Property(property="pix_key_type", type="string", enum={"cpf", "email", "phone", "random"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Conta PIX atualizada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao atualizar conta PIX"
     *     )
     * )
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $account = $this->pixAccountService->findByPixAccountId($request['auth_user'], $id);

            if (!$account) {
                return response()->json(['message' => 'Conta PIX não encontrada'], 404);
            }

            $validated = $request->validate([
                'pix_key' => 'sometimes|required|string|max:255',
                'pix_key_type' => 'sometimes|required|string|in:cpf,email,phone,random',
            ]);

            $updated = $this->pixAccountService->update($account, $validated);

            if (!$updated) {
                return response()->json(['message' => 'Erro ao atualizar conta PIX'], 500);
            }

            return response()->json(['message' => 'Conta PIX atualizada com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao atualizar conta PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/pix-accounts/{id}/deactivate",
     *     summary="Desativar conta PIX",
     *     tags={"Contas PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da conta PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Conta PIX desativada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao desativar conta PIX"
     *     )
     * )
     */
    public function deactivate(Request $request, int $id): JsonResponse
    {
        try {
            $account = $this->pixAccountService->findByPixAccountId($request['auth_user'], $id);

            if (!$account) {
                return response()->json(['message' => 'Conta PIX não encontrada'], 404);
            }

            $deactivated = $this->pixAccountService->deactivate($account);

            if (!$deactivated) {
                return response()->json(['message' => 'Erro ao desativar conta PIX'], 500);
            }

            return response()->json(['message' => 'Conta PIX desativada com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao desativar conta PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-accounts/{id}/balance",
     *     summary="Consultar saldo da conta PIX",
     *     tags={"Contas PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da conta PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saldo retornado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="balance", type="number", format="float")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao consultar saldo"
     *     )
     * )
     */
    public function balance(Request $request, int $id): JsonResponse
    {
        try {
            $account = $this->pixAccountService->findByPixAccountId($request['auth_user'], $id);

            if (!$account) {
                return response()->json(['message' => 'Conta PIX não encontrada'], 404);
            }

            $balance = $this->pixAccountService->getBalance($account);
            return response()->json(['balance' => $balance]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar saldo da conta PIX: ' . $e->getMessage()], 500);
        }
    }
}
