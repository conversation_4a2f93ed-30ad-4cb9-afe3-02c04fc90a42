<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\PixTransactionService;
use App\Services\PixAccountService;
use App\Models\PixTransaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

class PixTransactionController extends Controller
{
    public function __construct(
        private PixTransactionService $pixTransactionService,
        private PixAccountService $pixAccountService
    ) {}

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'pix_account_id' => 'required|integer|exists:pix_accounts,id',
            'type' => 'required|string|in:send,receive',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:1000',
            'pix_key' => 'required|string|max:255',
            'order_id' => 'nullable|string|max:255',
            'transaction_id_external' => 'nullable|string|max:255',
            'qr_code' => 'nullable|string',
        ]);

        $account = $this->pixAccountService->findByPixAccountId($request->user(), $validated['pix_account_id']);

        if (!$account) {
            return response()->json(['message' => 'Conta PIX não encontrada'], 404);
        }

        try {
            $transaction = $this->pixTransactionService->create($request->user(), $account, $validated);
            return response()->json($transaction, 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao criar transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-transactions",
     *     summary="Listar transações PIX",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="pix_account_id",
     *         in="query",
     *         description="ID da conta PIX para filtrar transações",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Status das transações para filtrar",
     *         @OA\Schema(type="string", enum={"pending", "completed", "failed"})
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Data inicial para filtrar transações",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="Data final para filtrar transações",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Lista de transações PIX retornada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="transactions",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="pix_account_id", type="integer"),
     *                     @OA\Property(property="amount", type="number", format="float"),
     *                     @OA\Property(property="description", type="string"),
     *                     @OA\Property(property="recipient_pix_key", type="string"),
     *                     @OA\Property(property="recipient_pix_key_type", type="string"),
     *                     @OA\Property(property="status", type="string"),
     *                     @OA\Property(property="created_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao listar transações PIX"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'status' => 'nullable|string|in:pending,completed,failed',
            'type' => 'nullable|string|in:send,receive',
        ]);

        try {
            $transactions = $this->pixTransactionService->list($request['auth_user'], $filters);
            return response()->json($transactions);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar transações PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-transactions/{id}",
     *     summary="Buscar transação PIX específica",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da transação PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Transação PIX encontrada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="pix_account_id", type="integer"),
     *             @OA\Property(property="amount", type="number", format="float"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="recipient_pix_key", type="string"),
     *             @OA\Property(property="recipient_pix_key_type", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="created_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Transação PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar transação PIX"
     *     )
     * )
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $transaction = $this->pixTransactionService->find($request->user(), $id);

            if (!$transaction) {
                return response()->json(['message' => 'Transação PIX não encontrada'], 404);
            }

            return response()->json($transaction);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-transactions/{id}/status",
     *     summary="Buscar status da transação PIX",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da transação PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Status da transação PIX retornado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Transação PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar status da transação PIX"
     *     )
     * )
     */
    public function status(Request $request, int $id): JsonResponse
    {
        try {
            $transaction = $this->pixTransactionService->find($request->user(), $id);

            if (!$transaction) {
                return response()->json(['message' => 'Transação PIX não encontrada'], 404);
            }

            return response()->json(['status' => $transaction->status]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar status da transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/pix-transactions/{id}/status",
     *     summary="Atualizar status da transação PIX",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da transação PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"status"},
     *             @OA\Property(property="status", type="string", enum={"pending", "completed", "failed"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Status atualizado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Transação PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao atualizar status da transação PIX"
     *     )
     * )
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|string|in:pending,completed,failed'
        ]);

        try {
            $transaction = $this->pixTransactionService->find($request->user(), $id);

            if (!$transaction) {
                return response()->json(['message' => 'Transação PIX não encontrada'], 404);
            }

            $updated = $this->pixTransactionService->updateStatus($transaction, $validated['status']);

            if (!$updated) {
                return response()->json(['message' => 'Erro ao atualizar status da transação PIX'], 500);
            }

            return response()->json(['message' => 'Status atualizado com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao atualizar status da transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @throws \Exception
     * @return JsonResponse<array{transaction: PixTransaction}|array{message: string}>
     */
    public function findByExternalId(Request $request, string $externalId): JsonResponse
    {
        try {
            $transaction = $this->pixTransactionService->findByExternalId($externalId);

            if (!$transaction) {
                return response()->json(['message' => 'Transação PIX não encontrada'], 404);
            }

            return response()->json($transaction);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/pix-transactions/deposit",
     *     summary="Realizar depósito PIX",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pix_account_id", "amount", "description"},
     *             @OA\Property(property="pix_account_id", type="integer", description="ID da conta PIX de destino"),
     *             @OA\Property(property="amount", type="number", format="float", description="Valor do depósito"),
     *             @OA\Property(property="description", type="string", maxLength=1000),
     *             @OA\Property(property="qr_code", type="string", description="QR Code do PIX (opcional)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Depósito PIX iniciado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="transaction", type="object"),
     *             @OA\Property(property="qr_code", type="string"),
     *             @OA\Property(property="pix_key", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao iniciar depósito PIX"
     *     )
     * )
     */
    public function deposit(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'pix_account_id' => 'required|integer|exists:pix_accounts,id',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:1000',
            'qr_code' => 'nullable|string'
        ]);

        $account = $this->pixAccountService->findByPixAccountId($request->user(), $validated['pix_account_id']);

        if (!$account) {
            return response()->json(['message' => 'Conta PIX não encontrada'], 404);
        }

        try {
            $transaction = $this->pixTransactionService->create($request->user(), $account, [
                'type' => 'deposit',
                'amount' => $validated['amount'],
                'description' => $validated['description'],
                'pix_key' => $account->pix_key,
                'qr_code' => $validated['qr_code'] ?? null
            ]);

            return response()->json([
                'transaction' => $transaction,
                'qr_code' => $transaction->qr_code,
                'pix_key' => $account->pix_key
            ], 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao iniciar depósito PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/pix-transactions/withdraw",
     *     summary="Realizar saque PIX",
     *     tags={"Transações PIX"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pix_account_id", "amount", "pix_key"},
     *             @OA\Property(property="pix_account_id", type="integer", description="ID da conta PIX de origem"),
     *             @OA\Property(property="amount", type="number", format="float", description="Valor do saque"),
     *             @OA\Property(property="description", type="string", maxLength=1000),
     *             @OA\Property(property="pix_key", type="string", description="Chave PIX do destinatário")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Saque PIX iniciado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="transaction", type="object"),
     *             @OA\Property(property="status", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Saldo insuficiente"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao iniciar saque PIX"
     *     )
     * )
     */
    public function withdraw(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'pix_account_id' => 'required|integer|exists:pix_accounts,id',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:1000',
            'pix_key' => 'required|string|max:255'
        ]);

        $account = $this->pixAccountService->findByPixAccountId($request->user(), $validated['pix_account_id']);

        if (!$account) {
            return response()->json(['message' => 'Conta PIX não encontrada'], 404);
        }

        // Verificar saldo
        if ($account->balance < $validated['amount']) {
            return response()->json(['message' => 'Saldo insuficiente para realizar o saque'], 400);
        }

        try {
            $transaction = $this->pixTransactionService->create($request->user(), $account, [
                'type' => 'withdraw',
                'amount' => $validated['amount'],
                'description' => $validated['description'],
                'pix_key' => $validated['pix_key']
            ]);

            // Atualizar saldo da conta
            $account->update([
                'balance' => $account->balance - $validated['amount']
            ]);

            return response()->json([
                'transaction' => $transaction,
                'status' => $transaction->status
            ], 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao iniciar saque PIX: ' . $e->getMessage()], 500);
        }
    }
}
