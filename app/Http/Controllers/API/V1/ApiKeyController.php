<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\ApiKeyService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

/**
 * @OA\SecurityScheme(
 *     securityScheme="apiKey",
 *     type="apiKey",
 *     in="header",
 *     name="X-API-KEY",
 *     description="API Key para autenticação"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="apiSecret",
 *     type="apiKey",
 *     in="header",
 *     name="X-API-SECRET",
 *     description="API Secret para autenticação"
 * )
 */
class ApiKeyController extends Controller
{
    public function __construct(
        private ApiKeyService $apiKeyService
    ) {}

    /**
     * @OA\Post(
     *     path="/api/v1/api-keys",
     *     summary="Criar nova API Key",
     *     tags={"API Keys"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name"},
     *             @OA\Property(property="name", type="string", maxLength=255),
     *             @OA\Property(property="description", type="string", maxLength=1000, nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="API Key criada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="key", type="string"),
     *             @OA\Property(property="secret", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="created_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao criar API Key"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        try {
            $apiKey = $this->apiKeyService->create($request->user(), $validated);
            return response()->json($apiKey, 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao criar API Key: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/api-keys",
     *     summary="Listar todas as API Keys do usuário",
     *     tags={"API Keys"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Lista de API Keys retornada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="api_keys",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="name", type="string"),
     *                     @OA\Property(property="key", type="string"),
     *                     @OA\Property(property="status", type="string"),
     *                     @OA\Property(property="created_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao listar API Keys"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $apiKeys = $this->apiKeyService->list($request->user());
            return response()->json($apiKeys);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar API Keys: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/api-keys/{id}",
     *     summary="Buscar API Key específica",
     *     tags={"API Keys"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da API Key",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API Key encontrada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="key", type="string"),
     *             @OA\Property(property="secret", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="created_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="API Key não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar API Key"
     *     )
     * )
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $apiKey = $this->apiKeyService->find($request->user(), $id);

            if (!$apiKey) {
                return response()->json(['message' => 'API Key não encontrada'], 404);
            }

            return response()->json($apiKey);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar API Key: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/api-keys/{id}/deactivate",
     *     summary="Desativar API Key",
     *     tags={"API Keys"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da API Key",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API Key desativada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="API Key não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao desativar API Key"
     *     )
     * )
     */
    public function deactivate(Request $request, int $id): JsonResponse
    {
        try {
            $apiKey = $this->apiKeyService->find($request->user(), $id);

            if (!$apiKey) {
                return response()->json(['message' => 'API Key não encontrada'], 404);
            }

            $deactivated = $this->apiKeyService->deactivate($apiKey);

            if (!$deactivated) {
                return response()->json(['message' => 'Erro ao desativar API Key'], 500);
            }

            return response()->json(['message' => 'API Key desativada com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao desativar API Key: ' . $e->getMessage()], 500);
        }
    }
}
