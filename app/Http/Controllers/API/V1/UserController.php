<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\UserService;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\User;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\ValidationException;
use OpenApi\Annotations as OA;

class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/users",
     *     summary="Listar usuários",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="is_active",
     *         in="query",
     *         description="Filtrar por ativo",
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Parameter(
     *         name="role",
     *         in="query",
     *         description="Filtrar por papel",
     *         @OA\Schema(type="string", enum={"user", "admin"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Lista de usuários retornada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="users",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="name", type="string"),
     *                     @OA\Property(property="email", type="string", format="email"),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao listar usuários"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->validate([
                'is_active' => 'nullable|boolean',
                'role' => 'nullable|string|in:user,admin',
            ]);

            $users = $this->userService->list($filters);
            return response()->json(['users' => UserResource::collection($users)]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar usuários: ' . $e->getMessage()], 500);
        }
    }

    public function list(Request $request): JsonResponse
    {
        $users = $this->userService->list($request->all());
        return response()->json($users, 200);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/{id}",
     *     summary="Obter dados de um usuário",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID do usuário",
     *         required=true,
     *         schema={"type": "integer"}
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Dados do usuário retornados com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="user",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="email", type="string", format="email"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar usuário"
     *     )
     * )
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $user = $this->userService->find($id);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            return response()->json(['user' => new UserResource($user)]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar usuário: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/users/{id}",
     *     summary="Atualizar dados de um usuário",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID do usuário",
     *         required=true,
     *         schema={"type": "integer"}
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="first_name", type="string", maxLength=255),
     *             @OA\Property(property="last_name", type="string", maxLength=255),
     *             @OA\Property(property="email", type="string", format="email", maxLength=255),
     *             @OA\Property(property="phone", type="string", maxLength=20),
     *             @OA\Property(property="document", type="string", maxLength=20)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Dados do usuário atualizados com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Dados inválidos"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao atualizar usuário"
     *     )
     * )
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $user = $this->userService->find($id);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            $validated = $request->validate([
                'first_name' => 'sometimes|required|string|max:255',
                'last_name' => 'sometimes|required|string|max:255',
                'email' => 'sometimes|required|email|max:255|unique:users,email,' . $id,
                'phone' => 'sometimes|required|string|max:20',
                'document' => 'sometimes|required|string|max:20|unique:users,document,' . $id,
            ]);

            $updated = $this->userService->update($user, $validated);

            if (!$updated) {
                return response()->json(['message' => 'Erro ao atualizar usuário'], 500);
            }

            return response()->json(['message' => 'Usuário atualizado com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao atualizar usuário: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/users/{id}",
     *     summary="Desativar um usuário",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID do usuário",
     *         required=true,
     *         schema={"type": "integer"}
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Usuário desativado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao desativar usuário"
     *     )
     * )
     */
    public function deactivate(Request $request, int $id): JsonResponse
    {
        try {
            $user = $this->userService->find($id);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            $deactivated = $this->userService->deactivate($user);

            if (!$deactivated) {
                return response()->json(['message' => 'Erro ao desativar usuário'], 500);
            }

            return response()->json(['message' => 'Usuário desativado com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao desativar usuário: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/users/{id}",
     *     summary="Ativar um usuário",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID do usuário",
     *         required=true,
     *         schema={"type": "integer"}
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Usuário ativado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao ativar usuário"
     *     )
     * )
     */
    public function activate(Request $request, int $id): JsonResponse
    {
        try {
            $user = $this->userService->find($id);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            $activated = $this->userService->activate($user);

            if (!$activated) {
                return response()->json(['message' => 'Erro ao ativar usuário'], 500);
            }

            return response()->json(['message' => 'Usuário ativado com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao ativar usuário: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/email",
     *     summary="Buscar usuário por email",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="email",
     *         in="query",
     *         description="Email do usuário",
     *         required=true,
     *         schema={"type": "string", "format": "email"}
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Usuário encontrado",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="user",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="email", type="string", format="email"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar usuário"
     *     )
     * )
     */
    public function findByEmail(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'email' => 'required|email|max:255',
            ]);

            $user = $this->userService->findByEmail($validated['email']);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            return response()->json(['user' => new UserResource($user)]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar usuário: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/document",
     *     summary="Buscar usuário por documento",
     *     tags={"Usuários"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="document",
     *         in="query",
     *         description="Documento do usuário",
     *         required=true,
     *         schema={"type": "string", "maxLength": 20}
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Usuário encontrado",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="user",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="email", type="string", format="email"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Usuário não encontrado"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao buscar usuário"
     *     )
     * )
     */
    public function findByDocument(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'document' => 'required|string|max:20',
            ]);

            $user = $this->userService->findByDocument($validated['document']);

            if (!$user) {
                return response()->json(['message' => 'Usuário não encontrado'], 404);
            }

            return response()->json(['user' => new UserResource($user)]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar usuário: ' . $e->getMessage()], 500);
        }
    }
}
